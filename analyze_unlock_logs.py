#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大型日志文件解锁次数分析脚本
分析指定时间范围内的解锁次数
"""

import re
import sys
from datetime import datetime
from typing import Optional, Tuple

class LogAnalyzer:
    def __init__(self, log_file_path: str):
        self.log_file_path = log_file_path
        self.unlock_count = 0
        self.start_time = None
        self.end_time = None
        
    def parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """解析时间戳字符串为datetime对象"""
        try:
            # 匹配格式: Wed Jan 01 2025 00:04:31 GMT+0800 (CST)
            pattern = r'(\w{3}) (\w{3}) (\d{2}) (\d{4}) (\d{2}):(\d{2}):(\d{2}) GMT\+0800 \(CST\)'
            match = re.match(pattern, timestamp_str.strip())
            if not match:
                return None
                
            weekday, month, day, year, hour, minute, second = match.groups()
            
            # 月份映射
            month_map = {
                'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
                'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
            }
            
            if month not in month_map:
                return None
                
            return datetime(
                int(year), month_map[month], int(day),
                int(hour), int(minute), int(second)
            )
        except Exception as e:
            print(f"时间解析错误: {timestamp_str} - {e}")
            return None
    
    def set_time_range(self, start_time_str: str, end_time_str: str):
        """设置分析的时间范围"""
        self.start_time = self.parse_timestamp(start_time_str)
        self.end_time = self.parse_timestamp(end_time_str)
        
        if not self.start_time or not self.end_time:
            raise ValueError("时间格式解析失败")
            
        print(f"分析时间范围: {self.start_time} 到 {self.end_time}")
    
    def is_unlock_log(self, log_block: str) -> bool:
        """判断日志块是否为解锁日志"""
        # 检查是否包含授权成功的标识
        if "req.client.authorized:" not in log_block:
            return False
            
        # 查找授权状态
        auth_match = re.search(r'req\.client\.authorized:\s*(\w+)', log_block)
        if not auth_match:
            return False
            
        is_authorized = auth_match.group(1).strip().lower() == 'true'
        
        # 检查是否有证书信息（非空的getPeerCertificate）
        has_cert = 'subject:' in log_block and 'issuer:' in log_block
        
        # 检查是否有成功的数据库插入
        has_insert = 'Rows inserted:' in log_block and 'Rows inserted: 1' in log_block
        
        return is_authorized and has_cert and has_insert
    
    def analyze_logs(self) -> int:
        """分析日志文件，返回解锁次数"""
        if not self.start_time or not self.end_time:
            raise ValueError("请先设置时间范围")
            
        print(f"开始分析日志文件: {self.log_file_path}")
        print("正在处理大型文件，请稍候...")
        
        current_log_block = ""
        current_timestamp = None
        in_time_range = False
        processed_lines = 0
        
        try:
            with open(self.log_file_path, 'r', encoding='utf-8', errors='ignore') as file:
                for line_num, line in enumerate(file, 1):
                    processed_lines += 1
                    
                    # 每处理10万行显示进度
                    if processed_lines % 100000 == 0:
                        print(f"已处理 {processed_lines:,} 行...")
                    
                    # 检查是否是新的日志块开始（以时间戳开头）
                    timestamp_match = re.match(r'^(\w{3} \w{3} \d{2} \d{4} \d{2}:\d{2}:\d{2} GMT\+0800 \(CST\))', line)
                    
                    if timestamp_match:
                        # 处理前一个日志块
                        if current_log_block and in_time_range and self.is_unlock_log(current_log_block):
                            self.unlock_count += 1
                            print(f"发现解锁 #{self.unlock_count}: {current_timestamp}")
                        
                        # 开始新的日志块
                        timestamp_str = timestamp_match.group(1)
                        current_timestamp = self.parse_timestamp(timestamp_str)
                        current_log_block = line
                        
                        # 检查是否在时间范围内
                        if current_timestamp:
                            in_time_range = self.start_time <= current_timestamp <= self.end_time
                        else:
                            in_time_range = False
                    else:
                        # 继续当前日志块
                        if in_time_range:
                            current_log_block += line
                
                # 处理最后一个日志块
                if current_log_block and in_time_range and self.is_unlock_log(current_log_block):
                    self.unlock_count += 1
                    print(f"发现解锁 #{self.unlock_count}: {current_timestamp}")
                    
        except FileNotFoundError:
            print(f"错误: 找不到文件 {self.log_file_path}")
            return 0
        except Exception as e:
            print(f"处理文件时发生错误: {e}")
            return 0
        
        print(f"\n分析完成！总共处理了 {processed_lines:,} 行")
        return self.unlock_count

def main():
    """主函数"""
    log_file = "log.log"
    
    # 设置时间范围
    start_time = "Wed Jan 01 2025 00:04:31 GMT+0800 (CST)"
    end_time = "Tue Jul 01 2025 23:56:36 GMT+0800 (CST)"
    
    print("=" * 60)
    print("大型日志文件解锁次数分析工具")
    print("=" * 60)
    
    try:
        analyzer = LogAnalyzer(log_file)
        analyzer.set_time_range(start_time, end_time)
        
        unlock_count = analyzer.analyze_logs()
        
        print("\n" + "=" * 60)
        print("分析结果:")
        print(f"时间范围: {start_time}")
        print(f"       到: {end_time}")
        print(f"解锁次数: {unlock_count}")
        print("=" * 60)
        
    except Exception as e:
        print(f"程序执行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
